import React from 'react';
import { Controller, Control, UseFormSetValue } from 'react-hook-form';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { BodyTypeEnum, BodyTypeLabel, ContentTypeEnum, getFormUrlencodedContentType } from '@common/constants/ExecutionConstants';
import { KanbanRadio } from 'kanban-design-system';
import { EXECUTION_API_AUTO_GENERATE, EXECUTION_API_CONTENT_TYPE } from '@pages/admins/executionConfig/Constants';

interface BodyTypeSelectorProps {
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
  isViewMode: boolean;
  headersFA: {
    fields: any[];
    append: (value: any) => void;
    remove: (index: number) => void;
  };
  onFieldChange?: () => void;
}

export const BodyTypeSelector: React.FC<BodyTypeSelectorProps> = ({ control, headersFA, isViewMode, onFieldChange, setValue }) => {
  return (
    <Controller
      name='apiInfo.body.bodyType'
      control={control}
      render={({ field }) => (
        <KanbanRadio
          group={{
            name: field.name,
            label: 'Body Type',
            value: String(field.value || ''),
            withAsterisk: false,
            display: 'row',
            readOnly: isViewMode,
            onChange: (val: string) => {
              field.onChange(val);

              const headers = headersFA.fields;
              const contentTypeHeaderIndex = headers.findIndex(
                (header: any) => header.key?.toLowerCase() === EXECUTION_API_CONTENT_TYPE.toLowerCase(),
              );

              if (val === BodyTypeEnum.RAW) {
                // Set default content type to TEXT when switching to RAW
                setValue('apiInfo.body.contentType', ContentTypeEnum.TEXT);
                // Ensure bodyRaw is not empty - set default if empty
                const currentBodyRaw = control._getWatch('apiInfo.body.bodyRaw');
                if (!currentBodyRaw || currentBodyRaw.trim() === '') {
                  setValue('apiInfo.body.bodyRaw', ' ');
                }
              } else if (val === BodyTypeEnum.URLENCODED) {
                const contentType = getFormUrlencodedContentType();
                if (contentTypeHeaderIndex >= 0) {
                  setValue(`apiInfo.headers.${contentTypeHeaderIndex}.value`, contentType);
                  setValue(`apiInfo.headers.${contentTypeHeaderIndex}.enable`, true);
                } else {
                  headersFA.append({
                    key: EXECUTION_API_CONTENT_TYPE,
                    value: contentType,
                    description: EXECUTION_API_AUTO_GENERATE,
                    enable: true,
                    autoGenerated: true,
                  });
                }
              } else if (val === BodyTypeEnum.NONE) {
                // Clear content type and raw body when switching to NONE
                setValue('apiInfo.body.contentType', undefined);
                setValue('apiInfo.body.bodyRaw', '');
                if (contentTypeHeaderIndex >= 0) {
                  headersFA.remove(contentTypeHeaderIndex);
                }
              }

              onFieldChange?.();
            },
          }}
          data={Object.entries(BodyTypeLabel).map(([value, label]) => ({
            value,
            label,
          }))}
        />
      )}
    />
  );
};
