import { UseFormSetValue } from 'react-hook-form';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { EXECUTION_API_AUTO_GENERATE } from '@pages/admins/executionConfig/Constants';
import { ExecutionModel } from '@models/ExecutionModel';

export interface ApiFormFieldArray {
  fields: any[];
  append: (value: any) => void;
  remove: (index: number) => void;
  insert?: (index: number, value: any) => void;
}

/**
 * Helper function to upsert auto-generated headers for API info form
 */
export function upsertAutoHeaderForApiInfo({
  headersFA,
  key,
  setValue,
  value,
}: {
  key: string;
  value: string;
  headersFA: ApiFormFieldArray;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
}) {
  const headers = headersFA.fields;
  const existingIndex = headers.findIndex((h: any) => h.key?.toLowerCase() === key.toLowerCase());

  if (value) {
    if (existingIndex >= 0) {
      const existing = headers[existingIndex];
      if (existing?.value !== value && existing?.autoGenerated === true) {
        setValue(`apiInfo.headers.${existingIndex}.key`, key);
        setValue(`apiInfo.headers.${existingIndex}.value`, value);
        setValue(`apiInfo.headers.${existingIndex}.enable`, true);
        setValue(`apiInfo.headers.${existingIndex}.description`, EXECUTION_API_AUTO_GENERATE);
        setValue(`apiInfo.headers.${existingIndex}.autoGenerated`, true);
      }
    } else {
      headersFA.insert?.(0, {
        key,
        value,
        description: EXECUTION_API_AUTO_GENERATE,
        enable: true,
        autoGenerated: true,
      }) ||
        headersFA.append({
          key,
          value,
          description: EXECUTION_API_AUTO_GENERATE,
          enable: true,
          autoGenerated: true,
        });
    }

    // Ensure exactly one empty row at the end
    ensureOneEmptyRowForApiInfo(headersFA);
  }
}

/**
 * Ensure exactly one empty row at the end of field array
 */
export function ensureOneEmptyRowForApiInfo(fieldArray: ApiFormFieldArray) {
  const fields = fieldArray.fields;

  let emptyRowsCount = 0;
  for (let i = fields.length - 1; i >= 0; i--) {
    const row = fields[i];
    if (!row?.key && !row?.value && !row?.description) {
      emptyRowsCount++;
    } else {
      break;
    }
  }

  while (emptyRowsCount > 1) {
    fieldArray.remove(fields.length - 1);
    emptyRowsCount--;
  }

  // Add one empty row if none exists
  if (emptyRowsCount === 0) {
    fieldArray.append({ key: '', value: '', description: '', enable: false });
  }
}

export const mergeApiInfoToExecution = (execution: ExecutionModel, apiInfo: ExecutionApiInfoModel): ExecutionModel => {
  return {
    ...execution,
    apiInfo,
  };
};

export const syncApiInfoBetweenForms = (sourceForm: any, targetForm: any, fieldPath: string = 'apiInfo') => {
  const apiInfo = sourceForm.getValues(fieldPath);
  targetForm.setValue(fieldPath, apiInfo);
};