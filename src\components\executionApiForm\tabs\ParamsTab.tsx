import React from 'react';
import { Control, UseFormSetValue, UseFormGetValues, UseFormSetFocus } from 'react-hook-form';
import { Box } from '@mantine/core';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { RenderTable } from './RenderTable';

interface ParamsTabProps {
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
  getValues: UseFormGetValues<{ apiInfo: ExecutionApiInfoModel }>;
  setFocus?: UseFormSetFocus<{ apiInfo: ExecutionApiInfoModel }>;
  paramsFA: {
    fields: any[];
    append: (value: any) => void;
    remove: (index: number) => void;
    insert?: (index: number, value: any) => void;
  };
  isViewMode?: boolean;
  variableNames?: string[];
  registerEditor?: (name: string, editor: any) => void;
  onFieldChange?: () => void;
}

const ParamsTab: React.FC<ParamsTabProps> = ({
  control,
  getValues,
  isViewMode = false,
  onFieldChange,
  paramsFA,
  registerEditor,
  setFocus,
  setValue,
  variableNames = [],
}) => {
  return (
    <Box p='md'>
      <RenderTable
        fa={paramsFA}
        control={control}
        namePrefix='apiInfo.params'
        isViewMode={isViewMode}
        executionParams={variableNames}
        getValues={getValues}
        setValue={setValue}
        registerEditor={registerEditor}
        setFocus={setFocus}
        onFieldChange={onFieldChange}
      />
    </Box>
  );
};

export default ParamsTab;
