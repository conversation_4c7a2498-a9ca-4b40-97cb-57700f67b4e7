import React, { useMemo } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { Controller, FormProvider, useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { KanbanButton, KanbanInput, KanbanSelect } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import SQLEditor from '@components/editor/SQLEditor';
import PythonEditor from '@components/editor/PythonEditor';
import { MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, EXECUTION_SCRIPT_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { ExecutionTypeEnum, ExecutionTypeLabel, HIDDEN_VARIABLE_PLACEHOLDER } from '@common/constants/ExecutionConstants';
import { ExecutionGroupApi } from '@api/ExecutionGroupApi';
import { DatabaseConnectionApi } from '@api/DatabaseConnectionApi';
import { VariableApi } from '@api/VariableApi';
import { ExecutionApi } from '@api/ExecutionApi';
import { getDefaultExecutionValues } from './ExcecutionDefaultValues';
import { ApiParamModel } from '@models/ExecutionApiKeyValueModel';
import { FormAction } from '@common/constants/BaseConstants';
import Section from './Section';
import ApiConfigSession from './ApiConfigSession';
import { ExecutionModel, ExecutionModelSchema } from '@models/ExecutionModel';
import { mergeApiInfoToExecution } from '@components/executionApiForm';

export default function ExecutionFormPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id?: string }>();
  const [searchParams] = useSearchParams();
  const execId = id && id !== '0' ? id : '';
  const isCreateMode = !execId;
  const actionParam = searchParams.get('action') as FormAction | null;
  const isViewMode = actionParam === FormAction.VIEW;
  const canEdit = isAnyPermissions([AclPermission.executionCreate, AclPermission.executionEdit]);
  const { data: execData } = useFetch(ExecutionApi.findById(execId), { enabled: !isCreateMode });

  const form = useForm<ExecutionModel>({
    defaultValues: getDefaultExecutionValues(ExecutionTypeEnum.PYTHON),
    resolver: zodResolver(ExecutionModelSchema),
    mode: 'onChange',
    values: execData?.data,
  });
  const { control, formState } = form;

  const executionGroupId = useWatch({ control, name: 'executionGroupId' });
  const executionGroupName = useMemo(() => execData?.data?.executionGroupName ?? '', [execData]);

  const { data: groupData } = useFetch(ExecutionGroupApi.findAll({ orderBy: 'name' }));
  const groupOptions = useMemo(() => {
    const groups = groupData?.data || [];
    const mapped = groups.map((g) => ({ label: g.name, value: g.id }));

    if (executionGroupId && !mapped.some((g) => g.value === executionGroupId) && executionGroupName) {
      mapped.push({ label: executionGroupName, value: executionGroupId });
    }

    return mapped;
  }, [groupData, executionGroupId, executionGroupName]);

  const { data: dbData } = useFetch(DatabaseConnectionApi.findAll({ orderBy: 'name' }), { showLoading: false });
  const dbOptions = useMemo(() => dbData?.data?.filter((c) => c.isActive).map((c) => ({ label: c.name, value: `${c.id}` })) || [], [dbData]);

  const { data: varData } = useFetch(VariableApi.findAll(), { showLoading: false });
  const variables = useMemo(
    () =>
      varData?.data?.map((v) => ({
        id: v.id,
        name: v.name,
        value: v.hidden ? HIDDEN_VARIABLE_PLACEHOLDER : v.value,
      })) || [],
    [varData],
  );

  const type = useWatch({ control, name: 'type' });

  const { mutate } = useMutate(ExecutionApi.createOrUpdate, {
    successNotification: isCreateMode ? 'Execution created.' : 'Execution updated.',
    onSuccess: () => navigate('../?tab=EXECUTION'),
  });

  const onSave = () => {
    const formData = form.getValues();

    if (formData.apiInfo) {
      const cleanedApiInfo = { ...formData.apiInfo };

      if (cleanedApiInfo.params) {
        cleanedApiInfo.params = cleanedApiInfo.params.filter(
          (param: ApiParamModel) => param.key?.trim() || param.value?.trim() || param.description?.trim(),
        );
      }

      if (cleanedApiInfo.headers) {
        cleanedApiInfo.headers = cleanedApiInfo.headers.filter(
          (header: ApiParamModel) => header.key?.trim() || header.value?.trim() || header.description?.trim(),
        );
      }

      if (cleanedApiInfo.body?.formUrlEncoded) {
        cleanedApiInfo.body.formUrlEncoded = cleanedApiInfo.body.formUrlEncoded.filter(
          (form: ApiParamModel) => form.key?.trim() || form.value?.trim() || form.description?.trim(),
        );
      }

      formData.apiInfo = cleanedApiInfo;
    }

    const parsed = ExecutionModelSchema.safeParse(formData);
    if (parsed.success) {
      mutate(parsed.data);
    }
  };

  return (
    <FormProvider {...form}>
      <Box p='sm' bg='white'>
        <Flex justify='space-between' mb='md'>
          <Title order={3}>{isViewMode ? 'View Execution' : isCreateMode ? 'Create Execution' : 'Edit Execution'}</Title>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../?tab=EXECUTION')}>
              Close
            </KanbanButton>
            {canEdit && (
              <KanbanButton onClick={onSave} disabled={!formState.isValid}>
                Save
              </KanbanButton>
            )}
          </Group>
        </Flex>

        <Stack gap='md'>
          <Section label='General Information'>
            <Stack gap='md'>
              <Controller
                name='name'
                control={control}
                render={({ field, fieldState }) => (
                  <KanbanInput
                    label='Config name'
                    required
                    {...field}
                    maxLength={MAX_CHARACTER_NAME_LENGTH}
                    description={getMaxLengthMessage(MAX_CHARACTER_NAME_LENGTH)}
                    error={fieldState.error?.message}
                    disabled={isViewMode}
                  />
                )}
              />
              <Controller
                name='description'
                control={control}
                render={({ field, fieldState }) => (
                  <KanbanInput
                    label='Description'
                    {...field}
                    maxLength={MAX_DESCRIPTION_LENGTH}
                    description={getMaxLengthMessage(MAX_DESCRIPTION_LENGTH)}
                    error={fieldState.error?.message}
                    disabled={isViewMode}
                  />
                )}
              />
              <Controller
                name='executionGroupId'
                control={control}
                render={({ field }) => <KanbanSelect label='Execution Group' required data={groupOptions} {...field} />}
                disabled={isViewMode}
              />
              <Controller
                name='type'
                control={control}
                render={({ field }) => (
                  <KanbanSelect
                    label='Type'
                    disabled={!isCreateMode}
                    required
                    data={Object.values(ExecutionTypeEnum).map((t) => ({ value: t, label: ExecutionTypeLabel[t] }))}
                    {...field}
                    onChange={(val) => {
                      if (val && val !== field.value) {
                        const currentValues = form.getValues();
                        const generalInfo = {
                          name: currentValues.name,
                          description: currentValues.description,
                          executionGroupId: currentValues.executionGroupId,
                        };

                        const newDefaultValues = getDefaultExecutionValues(val as ExecutionTypeEnum);
                        form.reset({
                          ...newDefaultValues,
                          ...generalInfo,
                          type: val as ExecutionTypeEnum,
                        });
                      }
                    }}
                  />
                )}
              />
            </Stack>
          </Section>

          {type === ExecutionTypeEnum.SQL && (
            <Section label='Database Connection'>
              <Controller
                name='databaseConnectionId'
                control={control}
                render={({ field: { onChange, value } }) => (
                  <KanbanSelect
                    label='Database Connection'
                    required
                    data={dbOptions}
                    value={value !== undefined ? `${value}` : undefined}
                    onChange={(v) => onChange(v ? parseInt(v) : undefined)}
                    disabled={isViewMode}
                    allowDeselect={false}
                  />
                )}
              />
            </Section>
          )}

          {(type === ExecutionTypeEnum.SQL || type === ExecutionTypeEnum.PYTHON) && (
            <Section label='Script'>
              <Controller
                name='script'
                disabled={isViewMode}
                control={control}
                render={({ field: { onChange, value } }) =>
                  type === ExecutionTypeEnum.SQL ? (
                    <SQLEditor value={value} onChange={onChange} height='350px' maxLength={EXECUTION_SCRIPT_MAX_LENGTH} />
                  ) : (
                    <PythonEditor value={value} onChange={onChange} variables={variables} height='350px' maxLength={EXECUTION_SCRIPT_MAX_LENGTH} />
                  )
                }
              />
            </Section>
          )}

          {type === ExecutionTypeEnum.API && (
            <ApiConfigSession
              form={mergeApiInfoToExecution.(form.getValues(), execData?.data.apiInfo)}
              isViewMode={isViewMode}
              executionParams={execData?.data?.variables?.map((v) => ({
                id: v.id ?? '',
                name: v.name,
                value: v.value ?? '',
                hidden: v.hidden ?? false,
              }))}
            />
          )}
        </Stack>
      </Box>
    </FormProvider>
  );
}
