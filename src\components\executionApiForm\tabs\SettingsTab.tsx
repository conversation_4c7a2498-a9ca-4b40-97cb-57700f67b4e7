import React from 'react';

import { Control, Controller, UseFormSetValue, UseFormGetValues } from 'react-hook-form';

import { Box, Flex } from '@mantine/core';

import { KanbanSelect, KanbanText, KanbanSwitch } from 'kanban-design-system';

import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';

import { HTTP_VERSION_OPTIONS } from '@common/constants/ExecutionConstants';

import classes from '../../../pages/admins/executionConfig/tabs/execution/ApiConfigSession.module.css';

interface SettingsTabProps {
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;

  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;

  getValues: UseFormGetValues<{ apiInfo: ExecutionApiInfoModel }>;

  isViewMode?: boolean;
}

const SettingsTab: React.FC<SettingsTabProps> = ({ control, isViewMode }) => {
  return (
    <Box className={classes.tabContent}>
      <Flex direction='column' gap='md'>
        <Flex align='center' gap='md'>
          <KanbanText size='sm' fw={500} style={{ minWidth: '120px' }}>
            HTTP Version:
          </KanbanText>

          <Controller
            name='apiInfo.httpVersion'
            control={control}
            render={({ field: { onChange, value } }) => (
              <KanbanSelect
                data={HTTP_VERSION_OPTIONS}
                value={value}
                onChange={(val) => onChange(val)}
                disabled={isViewMode}
                className={classes.settingsSelect}
              />
            )}
          />
        </Flex>

        <Flex align='center' gap='md'>
          <KanbanText size='sm' fw={500} style={{ minWidth: '120px' }}>
            Enable SSL:
          </KanbanText>

          <Controller
            name='apiInfo.enableSsl'
            control={control}
            render={({ field: { onChange, value } }) => (
              <KanbanSwitch checked={value} onChange={(event) => onChange(event.currentTarget.checked)} disabled={isViewMode} />
            )}
          />
        </Flex>
      </Flex>
    </Box>
  );
};

export default SettingsTab;
