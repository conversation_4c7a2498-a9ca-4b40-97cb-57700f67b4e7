import React, { useEffect } from 'react';
import { Control, Controller, UseFormSetValue, UseFormGetValues, UseFormSetFocus } from 'react-hook-form';
import { Box, Flex } from '@mantine/core';
import { KanbanSelect, KanbanButton } from 'kanban-design-system';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { BodyTypeEnum, ContentTypeEnum, CONTENT_TYPE_OPTIONS } from '@common/constants/ExecutionConstants';
import { beautifyRaw } from '@common/utils/BeautifyUtils';
import { EXECUTION_API_CONTENT_TYPE } from '@pages/admins/executionConfig/Constants';
import { upsertAutoHeaderForApiInfo, ApiFormFieldArray } from '../utils/ApiFormUtils';
import { getContentTypeValue, getFormUrlencodedContentType } from '@common/constants/ExecutionConstants';
import classes from './BodyTab.module.css';
import { ParamAwareTextarea } from '@pages/admins/executionConfig/tabs/execution/components';
import { RenderTable } from './RenderTable';
import { BodyTypeSelector } from './BodyTypeSelector';

interface BodyTabProps {
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
  getValues: UseFormGetValues<{ apiInfo: ExecutionApiInfoModel }>;
  setFocus?: UseFormSetFocus<{ apiInfo: ExecutionApiInfoModel }>;
  headersFA: ApiFormFieldArray;
  formFA: ApiFormFieldArray;
  isViewMode?: boolean;
  variableNames?: string[];
  registerEditor?: (name: string, editor: any) => void;
  onFieldChange?: () => void;
}

const BodyTab: React.FC<BodyTabProps> = ({
  control,
  formFA,
  getValues,
  headersFA,
  isViewMode = false,
  onFieldChange,
  registerEditor,
  setFocus,
  setValue,
  variableNames = [],
}) => {
  const bodyType = getValues('apiInfo.body.bodyType');
  const selectedContentType = getValues('apiInfo.body.contentType');

  // Auto-update Content-Type header when body type or content type changes
  useEffect(() => {
    if (isViewMode) {
      return;
    }

    let contentTypeValue = '';

    if (bodyType === BodyTypeEnum.RAW && selectedContentType) {
      contentTypeValue = getContentTypeValue(selectedContentType);
    } else if (bodyType === BodyTypeEnum.URLENCODED) {
      contentTypeValue = getFormUrlencodedContentType();
    }

    if (contentTypeValue) {
      upsertAutoHeaderForApiInfo({
        key: EXECUTION_API_CONTENT_TYPE,
        value: contentTypeValue,
        headersFA,
        setValue,
      });
    } else {
      const existing = headersFA.fields.findIndex((h: any) => h.key?.toLowerCase() === EXECUTION_API_CONTENT_TYPE.toLowerCase() && h.autoGenerated);
      if (existing >= 0) {
        headersFA.remove(existing);
      }
    }
  }, [bodyType, selectedContentType, isViewMode, headersFA, setValue]);

  // Auto-beautify when content type changes
  useEffect(() => {
    const autoBeautify = async () => {
      if (bodyType === BodyTypeEnum.RAW && selectedContentType && !isViewMode) {
        const bodyRawContent = getValues('apiInfo.body.bodyRaw');
        if (bodyRawContent) {
          try {
            const formatted = await beautifyRaw(String(bodyRawContent), selectedContentType);
            if (formatted !== bodyRawContent) {
              setValue('apiInfo.body.bodyRaw', formatted, { shouldDirty: false });
            }
          } catch (error) {
            console.warn('Auto-beautify failed:', error);
          }
        }
      }
    };

    if (selectedContentType) {
      autoBeautify();
    }
  }, [selectedContentType, bodyType, isViewMode, getValues, setValue]);

  return (
    <Box p='md'>
      <Box className={classes.bodyContainer}>
        <Flex align='flex-end' gap='md' wrap='wrap'>
          <BodyTypeSelector control={control} setValue={setValue} isViewMode={isViewMode} headersFA={headersFA} onFieldChange={onFieldChange} />

          {bodyType === BodyTypeEnum.RAW && (
            <Flex align='center' gap='sm' className={classes.rawControlsInline}>
              <Controller
                name='apiInfo.body.contentType'
                control={control}
                render={({ field }) => (
                  <KanbanSelect
                    {...field}
                    disabled={isViewMode}
                    data={CONTENT_TYPE_OPTIONS}
                    className={classes.contentTypeSelectInline}
                    size='xs'
                    allowDeselect={false}
                    w={100}
                    onChange={(value) => {
                      field.onChange(value);
                      onFieldChange?.();
                    }}
                  />
                )}
              />
              <KanbanButton
                size='xs'
                variant='light'
                className={classes.beautifyBtn}
                disabled={isViewMode}
                onClick={async () => {
                  const rawContent = getValues('apiInfo.body.bodyRaw');
                  const contentType = getValues('apiInfo.body.contentType') || selectedContentType || ContentTypeEnum.TEXT;
                  const formatted = await beautifyRaw(String(rawContent ?? ''), contentType);
                  setValue('apiInfo.body.bodyRaw', formatted, { shouldDirty: true });
                  onFieldChange?.();
                }}>
                Pretty
              </KanbanButton>
            </Flex>
          )}
        </Flex>

        {bodyType === BodyTypeEnum.RAW && (
          <Box>
            <Controller
              name='apiInfo.body.bodyRaw'
              control={control}
              render={({ field }) => (
                <ParamAwareTextarea
                  field={{ ...field, value: String(field.value ?? '') }}
                  disabled={isViewMode}
                  executionParams={variableNames}
                  contentType={selectedContentType}
                />
              )}
            />
          </Box>
        )}

        {bodyType === BodyTypeEnum.URLENCODED && (
          <Box>
            <RenderTable
              fa={formFA}
              control={control}
              namePrefix='apiInfo.body.formUrlEncoded'
              isViewMode={isViewMode}
              executionParams={variableNames}
              getValues={getValues}
              setValue={setValue}
              registerEditor={registerEditor}
              setFocus={setFocus}
              onFieldChange={onFieldChange}
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default BodyTab;
