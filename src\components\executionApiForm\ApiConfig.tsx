import React, { useCallback, useRef } from 'react';
import { Box } from '@mantine/core';
import { UseFormReturn } from 'react-hook-form';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { ApiConfigTabs } from './ApiConfigTabs';
import { ApiMethodUrlInput } from './ApiMethodUrlInput';
import { ExecutionParam } from '@core/schema/ExecutionParam';

interface ApiInfoFormProps {
  form: UseFormReturn<{ apiInfo: ExecutionApiInfoModel }>;
  isViewMode?: boolean;
  executionParams?: Array<ExecutionParam>;
  className?: string;
  variableNames?: string[];
  textareaRef?: React.MutableRefObject<HTMLElement | null>;
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
}

const ApiInfoForm: React.FC<ApiInfoFormProps> = ({
  className,
  form,
  isViewMode = false,
  registerEditor: propRegisterEditor,
  textareaRef: propTextareaRef,
  variableNames = [],
}) => {
  const editorMapRef = useRef<Record<string, { insert: (text: string, pos: number) => void }>>({});
  const defaultTextareaRef = useRef<HTMLElement | null>(null);
  const textareaRef = propTextareaRef || defaultTextareaRef;

  const defaultRegisterEditor = useCallback((fieldName: string, insertFn: (text: string, pos: number) => void) => {
    editorMapRef.current[fieldName] = {
      insert: (text, pos) => {
        insertFn(text, pos);
      },
    };
  }, []);

  const registerEditor = propRegisterEditor || defaultRegisterEditor;

  return (
    <Box className={className}>
      <ApiMethodUrlInput form={form} isViewMode={isViewMode} registerEditor={registerEditor} executionParams={variableNames} />

      <ApiConfigTabs form={form} isViewMode={isViewMode} textareaRef={textareaRef} registerEditor={registerEditor} variableNames={variableNames} />
    </Box>
  );
};

export default ApiInfoForm;
